@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

html {
  box-sizing: border-box;
}

body {
  overflow-x: hidden;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/**
 * Basic styles for links
 */
a {
  color: $text-color;
  text-decoration: none;
  cursor: pointer;
  @include on-event {
    color: $text-color;
    text-decoration: none;
  }
}
/** 
  * Container
  */

@media (min-width: 1200px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl {
      max-width: 1300px;
  }
}