@charset "UTF-8";

// 1. Configuration and helpers
@use 'abstracts/variables' as *;
@use 'abstracts/mixins' as *;
@use 'abstracts/general' as *;
@use 'abstracts/utilites' as *;

// 2. Base stuff
@use 'base/reset' as *;
@use 'base/typography' as *;
@use 'base/base' as *;
@use 'base/common' as *;

// 3. Layout-related sections
@use 'layout/header' as *;
@use 'layout/footer' as *;

// 4. Components
@use 'components/button' as *;
@use 'components/card' as *;
@use 'components/carousel' as *;
@use 'components/mega-menu' as *;
@use 'components/industry-menu' as *;
@use 'components/suppliers' as *;
@use 'components/pos-apps' as *;

// 5. Page-specific styles
@use 'pages/home' as *;
@use 'pages/module' as *;
@use 'pages/industry' as *;
@use 'pages/features' as *;
@use 'pages/pricing' as *;
@use 'pages/affiliates-program' as *;
@use 'pages/success-partners' as *;
@use 'pages/agencies-program' as *;
@use 'pages/about' as *;
@use 'pages/contact' as *;
@use 'pages/agencies' as *;
@use 'pages/our-clients' as *;
@use 'pages/gs1-search-result' as *;
@use 'pages/landing' as *;
@use 'pages/daftra-changelog' as *;

// 6. RTL
@use 'base/rtl' as *;